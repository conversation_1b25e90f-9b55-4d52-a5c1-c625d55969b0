<template>
  <div v-if="visible" class="dropdown-overlay" @click="handleClose" :style="{ top: headHeight + 'px' }">
    <div class="dropdown-content" @click.stop>
      <div class="chain-wrap">
        <!-- 父级列表 -->
        <div class="parent">
          <div
            v-for="(item, index) in data.sourceData"
            :key="index"
            @click="handleChange(item, index)"
            :class="{ item: true, active: item.active }"
          >
            <div class="name text-ellipsis">{{ item.name }}</div>
          </div>
        </div>
        <!-- 子级列表 -->
        <div class="children">
          <div
            v-for="(child, childIndex) in data.activeChildList"
            :key="childIndex"
            @click="handleChange(child)"
            class="child-item text-ellipsis"
          >
            <span class="child-name" :class="{ active: child.active }">{{ child.name }}</span>
            <img v-if="child.active" src="@/assets/image/cyl/i_gougou.png" alt="选中" class="check-icon" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch, onBeforeMount } from 'vue'
import { getChainList } from '@/api/iChain/index'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  selectedData: {
    type: Array,
    default: () => [],
  },
  headHeight: {
    type: Number,
    default: 88,
  },
})

const emits = defineEmits(['close', 'submit'])

const data = reactive({
  activeChildList: [], // 处于活动状态的子级列表
  sourceData: [],
  dataLoaded: false, // 数据是否已加载完成
})

/**
 * 获取数据
 */
const getAllChainData = async () => {
  try {
    const res = await getChainList()
    console.log('获取产业链数据:', res)
    // 处理数据格式
    const chainData = res || []

    // 添加全部选项
    const processedData = [
      ...chainData.map(chain => ({
        ...chain,
        code: chain.chain_code,
        active: false,
        children: chain.children || [],
      })),
    ]

    data.sourceData = processedData
    data.dataLoaded = true // 标记数据已加载完成
    console.log('处理后的数据:', data.sourceData)

    // 数据加载完成后，如果有选中数据且弹窗可见，则设置回显
    if (props.visible && props.selectedData.length > 0) {
      setEchoData()
    }
  } catch (error) {
    console.error('获取产业链数据失败:', error)
    data.sourceData = []
    data.dataLoaded = true
  }
}

/**
 * 选中切换
 */
const handleChange = (select, parentind) => {
  // 如果是点击父级（第一级）
  if (parentind !== undefined) {
    // 更新父级选中状态
    data.sourceData.forEach((item, index) => {
      item.active = index === parentind
    })

    // 加载子级数据
    const selectedParent = data.sourceData[parentind]
    if (selectedParent && selectedParent.children) {
      data.activeChildList = selectedParent.children.map(child => ({
        ...child,
        active: false,
      }))
    } else {
      data.activeChildList = []
    }
  } else {
    // 如果是点击子级（第二级）
    // 先更新子级选中状态
    data.activeChildList.forEach(child => {
      child.active = child.code === select.code || child.chain_code === select.code
    })

    // 延迟一下让用户看到选中效果，然后关闭并传值
    setTimeout(() => {
      const activeParent = data.sourceData.find(item => item.active)
      const selectedChild = {
        ...select,
        parent_code: activeParent ? activeParent.code || activeParent.chain_code : null,
      }
      emits('submit', [selectedChild])
    }, 200)
  }
}

/**
 * 设置回显数据
 */
const setEchoData = () => {
  if (!data.dataLoaded || props.selectedData.length === 0) {
    return
  }

  const selectedItem = props.selectedData[0]
  console.log('设置回显数据:', selectedItem)

  // 找到对应的父级
  let parentIndex = -1
  let targetParent = null

  // 遍历所有父级，找到包含选中项的父级
  data.sourceData.forEach((parent, index) => {
    if (
      parent.children &&
      parent.children.some(child => child.code === selectedItem.code || child.chain_code === selectedItem.code)
    ) {
      parentIndex = index
      targetParent = parent
    }
  })

  if (parentIndex !== -1 && targetParent) {
    // 先重置所有父级状态
    data.sourceData.forEach((item, index) => {
      item.active = index === parentIndex
    })

    // 设置子级列表，并标记选中的子项
    data.activeChildList = targetParent.children.map(child => ({
      ...child,
      active: child.code === selectedItem.code || child.chain_code === selectedItem.code,
    }))

    console.log('设置的父级索引:', parentIndex)
    console.log('设置的子级列表:', data.activeChildList)
  }
}

/**
 * 重置所有状态
 */
const resetAllStates = () => {
  data.sourceData.forEach(item => {
    item.active = false
  })
  data.activeChildList = []
}

/**
 * 关闭下拉框
 */
const handleClose = () => {
  emits('close')
}

// 监听显示状态
watch(
  () => props.visible,
  val => {
    if (val) {
      resetAllStates()
      // 如果数据已加载且有选中数据，设置回显
      if (data.dataLoaded && props.selectedData.length > 0) {
        setEchoData()
      }
    }
  },
  {
    immediate: true,
  }
)

// 监听选中数据变化
watch(
  () => props.selectedData,
  () => {
    // 如果弹窗可见且数据已加载，设置回显
    if (props.visible && data.dataLoaded && props.selectedData.length > 0) {
      setEchoData()
    }
  },
  {
    deep: true,
    immediate: true,
  }
)

//生命周期
onBeforeMount(() => {
  getAllChainData()
})
</script>

<style lang="scss" scoped>
.dropdown-overlay {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  // height: calc(100vh - 300px);
  background: rgba(0, 0, 0, 0.2);
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.dropdown-content {
  width: 100vw;
  max-width: 100vw;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 0 0 16px 16px;
  overflow: hidden;
}

.chain-wrap {
  background-color: #fff;
  color: #20263a;
  font-size: 26px;
  position: relative;
  overflow-x: hidden;
}

.parent {
  width: 308px;
  height: 756px;
  background-color: rgba(247, 247, 247, 0.6);
  overflow-y: scroll;
  scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE 10+ */
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
}

.item {
  height: 84px;
  line-height: 84px;
  padding-left: 24px;
  padding-right: 24px;
  font-size: 26px;
  color: #20263a;
  font-family: PingFang SC-Regular, PingFang SC;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.item.active {
  background-color: #fff;
  color: #07a6f0;
  font-weight: 600;
  font-family: PingFang SC-Semibold, PingFang SC;
}

.item .name {
  width: 240px;
  font-weight: 400;
  font-size: 26px;
  color: #404040;
}

.text-ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.check-icon {
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

// 子级项目样式
.child-item {
  height: 84px;
  line-height: 84px;
  padding-left: 24px;
  padding-right: 24px;
  font-size: 26px;
  color: #20263a;
  font-family: PingFang SC-Regular, PingFang SC;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .child-name {
    flex: 1;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;

    &.active {
      color: #07a6f0;
      font-weight: 600;
      font-family: PingFang SC-Semibold, PingFang SC;
    }
  }
}

.children {
  height: 400px;
  width: 100%;
  position: absolute;
  left: 308px;
  top: 0;
  overflow-y: scroll;
  scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE 10+ */
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
}
</style>

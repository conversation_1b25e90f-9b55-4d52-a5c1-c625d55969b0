<template>
  <div class="cards">
    <!-- 标题 -->
    <div class="title">
      <div>技术研发</div>
      <div class="flex items-center cursor-pointer">
        <u>查看企业</u><svg-icon name="arrow" class="w-20 h-20 ml-8" />
      </div>
    </div>
    <div class="flex flex-col">
      <!-- 第一级 占一行-->
      <div :class="['level1']">生物技术</div>
      <div class="flex flex-wrap">
        <!-- 第二级 自身宽度 -->
        <div :class="['level2']">我是修喜喜</div>
        <div :class="['level2']">我是修喜喜</div>
        <div :class="['level2']">我是修喜喜</div>
        <div :class="['level2']">我是修喜喜</div>
      </div>
    </div>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped>
.cards {
  background: #fff;
  padding: 32px 24px;
  .title {
    @apply flex justify-between items-center h-40;
    div:first-child {
      position: relative;
      font-weight: 600;
      font-size: 28px;
      color: #404040;
      text-align: left;
      padding-left: 20px;
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 28px;
        background: linear-gradient(90deg, #4ab8ec 0%, #07a6f0 100%);
        border-radius: 2px;
      }
    }

    div:last-child {
      font-weight: 400;
      font-size: 24px;
      color: #b2b2b2;
    }
  }

  .level1 {
    display: flex;
    align-items: center;
    height: 56px;
    background: #f7f7f7;
    border-radius: 8px 8px 8px 8px;
    padding: 0 20px;
    margin-top: 32px;
  }
  .level2 {
    display: flex;
    align-items: center;
    height: 56px;
    background: #f7f7f7;
    border-radius: 8px 8px 8px 8px;
    padding: 0 20px;
    margin-top: 32px;
    width: auto;
    padding: 0 20px;
    margin-top: 20rpx;
    gap: 20px;
  }
}
</style>

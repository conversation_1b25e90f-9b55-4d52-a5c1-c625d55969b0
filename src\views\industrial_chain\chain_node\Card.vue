<template>
  <div>
    <div class="cards" v-for="item in list" :key="item.chain_code">
      <!-- 标题 -->
      <div class="title">
        <div>{{ item.name }}</div>
        <div class="flex items-center cursor-pointer">
          <u>查看企业</u><svg-icon name="arrow" class="w-20 h-20 ml-8" />
        </div>
      </div>
      <div class="flex flex-col" v-for="itm in item.children" :key="itm.chain_code">
        <!-- 第一级 占一行-->
        <div :class="['level1', false && 'active']">{{ itm.name }}</div>
        <div class="flex flex-wrap level2-container" v-if="itm.children.length">
          <!-- 第二级 自身宽度 -->
          <div :class="['level2', false && 'active']" v-for="i in itm.children" :key="i.chain_code">
            {{ i.name }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  source: {
    type: Array,
    default: () => [],
  },
})
const list = computed(() => {
  return props.source
})
</script>

<style lang="scss" scoped>
.cards {
  background: #fff;
  padding: 32px 24px;
  .title {
    @apply flex justify-between items-center h-40;
    div:first-child {
      position: relative;
      font-weight: 600;
      font-size: 28px;
      color: #404040;
      text-align: left;
      padding-left: 20px;
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 28px;
        background: linear-gradient(90deg, #4ab8ec 0%, #07a6f0 100%);
        border-radius: 2px;
      }
    }

    div:last-child {
      font-weight: 400;
      font-size: 24px;
      color: #b2b2b2;
    }
  }

  .level1 {
    display: flex;
    align-items: center;
    height: 56px;
    background: #f7f7f7;
    border-radius: 8px 8px 8px 8px;
    padding: 0 20px;
    margin-top: 32px;
    font-weight: 400;
    font-size: 28px;
    color: #404040;
    &.active {
      color: #07a6f0;
      background: #f0faff;
    }
  }

  .level2-container {
    gap: 20px;
  }
  .level2 {
    display: flex;
    align-items: center;
    height: 56px;
    background: #f7f7f7;
    border-radius: 8px;
    padding: 0 20px;
    margin-top: 20px;
    width: auto;
    font-weight: 400;
    font-size: 24px;
    color: #404040;
    &.active {
      color: #07a6f0;
      background: #f0faff;
    }
  }
}
</style>

<template>
    <svg :class="svgClass">
        <use :xlink:href="iconName" />
    </svg>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
    name: {
        type: String,
        required: true
    }
})
const iconName = computed(() => `#icon-${props.name}`)
const svgClass = computed(() => {
    if (props.name) {
        return `svg-icon icon-${props.name} ${props.class}`
    }
    return 'svg-icon'
})
</script>

<style lang="scss">
.svg-icon {
    fill: currentColor;
    vertical-align: middle;
    width: 36px;
    height: 36px;
}
</style>

<script setup name="AtlasList">
import { useRoute, useRouter } from 'vue-router'
import { useRect, useWindowSize } from '@vant/use'
import Map from '../../overviewOfCQEnt/Map.vue'
import RadioArea from '@/components/Hunt/component/radioIndustry/index.vue'
import RadioIndustry from '@/components/Hunt/component/radioIndustry/indexIndustry.vue'
import { constant } from '@/components/Hunt/component/MultiplecChoice/utils'
import { setPx } from '@/utils/height'
import { goH5 } from '@/utils/utils'
const { width, height } = useWindowSize()
const route = useRoute()
const MDUTCG_MAP = ['北京市', '重庆市', '天津市', '上海市']
const CHINA_CODE = '1000000'
const LEVEL_MAP = {
  province: '1',
  city: '2',
  district: '3',
}
const data = reactive({
  //请求的相关
  paramsNew: {
    chain_codes: [],
    page_index: 1,
    page_size: 10,
  },
  paramsData: {},
  sortVisible: false,
  popSort: [
    {
      name: '产业结构',
      show: false,
      isVal: false,
    },
    {
      name: '全国',
      show: false,
      isVal: false,
    },
  ],
  company_num: 0,
  // 弹窗相关
  region: {
    name: '全国',
    code: CHINA_CODE,
  },
  chain_codes: [],
  chain_code: '',
  agg_type: 2,
  tagArr: [],
  // 高度
  flistHeight: 0,
  listHeight: 0,
  huntHeight: 0,
  list: [], //热门推荐
})
const listRef = ref(null)
const huntRef = ref(null)
const router = useRouter()
const mapShow = ref(true)
const { proxy } = getCurrentInstance()
// 获取相关高度
const getHeight = () => {
  let mapRef = useRect(listRef)
  data.flistHeight = mapRef.top
  data.listHeight = height.value - data.flistHeight - mapRef.height
  data.huntHeight = height.value - data.flistHeight
}
const _getHeatMapData = async (params = {}) => {
  const payload = {
    chain_codes: data.chain_codes,
    agg_type: 2,
    areas: [],
    ...params,
  }
  let count = 0
  let [err, res] = await proxy.$to('getXNYQC', payload)
  const result = res?.datalist || []

  let heatMapData = result
    .map(item => {
      // 无需过滤冗余数据
      // if (payload.areas[0] !== undefined) {
      //   const findIndex = provinces.slice(1).findIndex(province => province.fullName === item.region_name);
      //   if (findIndex > -1) return;
      // }
      return {
        value: item.count,
        name: item.region_name,
      }
    })
    .filter(Boolean)
  if (result.length) {
    count = result.map(i => i.count).reduce((n, m) => n + m)
  }
  data.hotData = heatMapData
  data.company_num = count
}
const queryData = region => {
  let agg_type = region.code === 'ALL' ? 2 : region.level === '1' ? 4 : 6
  if (region.code === 'ALL' /** 首次进入页面或点击地区选择器 全国 选项 */) {
    _getHeatMapData({
      areas: [],
      agg_type,
    })
  } else if (MDUTCG_MAP.includes(region.name) /** 选择的是直辖市， 可触发条件：点击地图块，地区选择器 */) {
    _getHeatMapData({
      areas: [region.code],
      agg_type,
    })
  } else if (region.level === '1' /** 选择的是省份， 可触发条件同上 */) {
    _getHeatMapData({
      areas: [region.code],
      agg_type,
    })
  } else if (region.level === '2' /** 选择的是市级，可触发条件同上 */) {
    _getHeatMapData({
      areas: [region.code],
      agg_type,
    })
  } /** 区县级，可触发条件同上 */ else {
    _getHeatMapData({
      areas: [region.code],
      agg_type,
    })
  }
  data.agg_type = agg_type
}

// 获取初始化参数-请求相关
const getParams = () => {
  let arr = JSON.parse(decodeURIComponent(route.query.arr))
  let { high, leader_count } = arr[arr.length - 1],
    tagArr = []
  if (arr.length > 1) {
    high == '1' && tagArr.push('高价值产业环节')
    leader_count && tagArr.push('有龙头企业')
    data.chain_code = arr[arr.length - 1].code
    data.chain_codes = [arr[arr.length - 1].code]
    data.popSort[0].isVal = arr.length > 1
    data.fchain_codes = [arr[0].code] //这个不会变
    data.routeList = arr
    data.tagArr = tagArr
    // 企业推荐
    // 获取地区数组
    queryData({ code: 'ALL' })
  }
}

// 筛选点击
const headTap = item => {
  let { popSort } = data
  let isshow = !popSort[item].show
  popSort.forEach(item => {
    item.show = false
  })
  popSort[item].show = isshow
  data.popSort = popSort
}
// 列表展示
const backRoute = () => {
  let { routeList } = data,
    arr = decodeURIComponent(JSON.stringify(routeList))
  router.replace({
    path: '/home/<USER>/list',
    query: {
      arr: arr,
    },
  })
}
// 回调
const chooseSort = item => {}
// 取消
const resetCondition = () => {}
// 确定 地图回调
const clickMap = item => {
  try {
    let datas = item?.data || {}
    let code = String(datas?.adcode || datas?.id)
    if (!code) return
    const level = LEVEL_MAP[datas.level]
    let curRegion = {
      code,
      level,
      parent: datas?.parent?.adcode + '',
      name: datas.name,
    }
    data.region = curRegion
    data.popSort[1].name = curRegion.name
    data.popSort[1].isVal = curRegion.name != '全国'
    queryData(curRegion)
  } catch (err) {}
}
const getRegion = item => {
  try {
    data.popSort[1].show = false
    let curRegion = item?.length ? item[0] : item
    data.region = curRegion
    data.popSort[1].name = curRegion.name
    data.popSort[1].isVal = curRegion.name != '全国'
    queryData(curRegion)
  } catch (err) {
    console.log('210', err)
    data.popSort[1].show = false
  }
}
const getqcCy = oldAry => {
  let datas = oldAry?.length ? oldAry[0] : oldAry
  let { chain_codes, popSort, region } = data
  popSort[0].show = false
  popSort[0].isVal = datas.name != '智能网联新能源汽车产业'
  popSort[0].name =
    datas.chain_code == 'ALL' ? '产业结构' : datas.name.length > 7 ? datas.name.slice(0, 7) + '...' : datas.name
  if (datas.chain_code == 'ALL') {
    chain_codes = data.fchain_codes
  } else {
    chain_codes = datas.chain_code ? [datas.chain_code] : []
  }
  data.popSort = popSort
  data.chain_codes = chain_codes || 'E1E1'
  _getHeatMapData({
    areas: datas.chain_code == 'ALL' ? [] : region.code == '1000000' ? [] : [region.code],
    agg_type: data.agg_type,
  })
}
const getqcCyAry = oldAry => {
  let { routeList } = data
  let arr = []
  routeList = routeList.slice(0, 1)
  let datas = oldAry?.length ? oldAry[0] : oldAry
  if (!oldAry?.length) return
  if (datas.chain_code != 'ALL') {
    arr = oldAry.map(item => {
      return {
        code: item.chain_code,
        high: item.high,
        leader_count: item.leader_count,
        name: item.name,
        strong_codes: item.strong_codes,
        weak_codes: item.weak_codes,
      }
    })
    routeList.push(...arr)
  }
  data.routeList = routeList
}
//
const getHotList = async () => {
  //如果要联动每次筛选了getqcCy都要掉一次 还要排除ALL
  const [err, res] = await proxy.$to('qiyeHot', {
    code: data.chain_code,
    page_index: 1,
    page_size: 10,
  })
  if (err || !res?.datalist?.length) {
    proxy.$close()
    return
  }
  data.list = res.datalist
  proxy.$close()
}
const goDetail = item => {
  //进企业详情
  if (!item.ent_id) return
  goH5({ entId: item.ent_id })
}
/**
 * 没发现地图
 */
const onFound = () => {
  mapShow.value = false
}
onMounted(() => {
  proxy.$loading('加载中...')
  getParams()
  getHotList()
  getHeight()
})
</script>

<template>
  <div class="leading-page">
    <!-- 头部标签部分 -->
    <div class="biaoqianf">
      <div class="biaoqian">
        <template v-for="(item, index) in data.routeList" :key="index">
          <span :class="[index == data.routeList.length - 1 && 'one']">
            {{ item.name }}
          </span>
          <span class="two" v-if="index != data.routeList.length - 1"></span>
        </template>
      </div>
    </div>
    <!-- 筛选部分 -->
    <div class="twonav">
      <div class="company_num">
        共有 <span class="color_num">{{ data.company_num }}</span> 家企业
      </div>
      <div class="head_left">
        <div
          class="left_box"
          v-for="(item, index) in data.popSort"
          :key="index"
          :class="{ active: item.show || item.isVal }"
          @click="headTap(index)"
        >
          <span class="down_txt">{{ item.name }}</span>
          <div class="iconas flex_all_center" v-if="data.levelShow">
            <img :src="icon || '@assets/img/tupu/menu-up.png'" />
          </div>
          <img v-if="!item.show && !item.isVal" class="down_img" src="@/assets/img/tupu/menu-down.png" />
          <img v-if="!item.show && item.isVal" class="down_img" src="@/assets/img/tupu/menu-down-a.png" />
          <img v-if="item.show" class="down_img" src="@/assets/img/tupu/menu-up.png" />
        </div>
      </div>
    </div>
    <!-- 地图点击部分 -->
    <div class="threenav" ref="listRef" id="threenav">
      <template v-if="!data.tagArr.length">
        <view style="border: none"></view>
      </template>
      <template v-if="data.tagArr.length">
        <span v-for="(item, index) in data.tagArr" :key="index">{{ item }}</span>
      </template>
      <span class="end" @click="backRoute()">列表展示</span>
    </div>
    <!-- 列表 -->
    <div class="list" :style="{ height: data.listHeight + 'px' }">
      <!-- 地图 -->
      <div class="map">
        <!--  -->
        <Map
          v-show="mapShow"
          :areaCode="data.region"
          @onFound="onFound"
          isClick
          @clickMap="clickMap"
          :heatMapData="data.hotData"
        />
        <div v-show="!mapShow" class="imgs">
          <img src="@/assets/img/no-data.png" />
          <!-- <div>地图走丢...</div> -->
        </div>
      </div>
      <!-- 企业推荐 -->
      <div class="touzi" v-if="data.list.length">
        <div class="touzi-t">推荐企业</div>
        <div class="touzi-c">
          <div v-for="(item, index) in data.list" :key="index">
            <div @click="goDetail(item)" class="touzi-item">{{ item.ent_name }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 排序弹窗 -->
  <van-popup
    v-model:show="data.popSort[0].show"
    :teleport="listRef"
    position="top"
    round
    close-on-popstate
    close-on-click-overlay
    :style="{ height: setPx(600) + 2 + 'px', top: data.flistHeight - 3 + 'px' }"
  >
    <div class="huntWrap">
      <RadioIndustry
        :visible="data.popSort[0].show"
        @close="() => (data.popSort[0].show = false)"
        headText="智能网联新能源汽车"
        childText=" "
        :dataType="constant.Industry"
        @submit="getqcCy"
        @submitAry="getqcCyAry"
        :oldData="data.chain_codes[0] == 'E1E1' ? 'ALL' : data.chain_codes[0]"
      />
    </div>
  </van-popup>
  <!-- 筛选 -->
  <van-popup
    v-model:show="data.popSort[1].show"
    :teleport="listRef"
    position="top"
    round
    close-on-popstate
    close-on-click-overlay
    :style="{ height: setPx(600) + 2 + 'px', top: data.flistHeight - 3 + 'px' }"
  >
    <div class="huntWrap">
      <RadioArea
        :visible="data.popSort[1].show"
        @submit="getRegion"
        @close="() => (data.popSort[1].show = false)"
        headText="全国"
        childText="全部"
        :oldData="data.region.code"
      />
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>
/* 返回路由样式 */
.biaoqianf {
  scroll-behavior: smooth;
  overflow-x: scroll;
  width: 100vw;
  white-space: nowrap;
  background: #f7f7f7;
  position: relative;
  z-index: 2100;
  -ms-overflow-style: none; /* IE 10+ */
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }

  .biaoqian {
    display: inline-flex;
    align-items: center;
    height: 82px;
    padding-left: 24px;
    padding-right: 24px;
    font-size: 24px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #9b9eac;
    width: auto;
    overflow-x: scroll;

    & .one {
      color: #3d4255;
    }

    .two {
      display: inline-block;
      margin: 0 8px;
      width: 16px;
      height: 2px;
      background: #9b9eac;
    }
  }
}
/*  */
.twonav {
  position: relative;
  height: 96px;
  box-sizing: border-box;
  display: flex;
  padding: 0 24px;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  position: relative;
  z-index: 2100;
  border-bottom: 1px solid #f7f7f7;
  box-sizing: border-box;

  .company_num {
    font-size: 28px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 600;
    text-align: center;
    color: #20263a;

    .color_num {
      color: #076ee4;
    }
  }

  .head_left {
    min-width: 246px;
    height: 96px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left_box {
      font-size: 28px;
      font-family: PingFang SC, PingFang SC-Regular;
      font-weight: 400;
      text-align: LEFT;
      color: #74798c;
      margin-right: 8px;

      &.active {
        color: #076ee4;
      }

      .down_txt {
        padding-right: 12px;
      }

      .down_img {
        width: 20px;
        height: 20px;
      }
    }
  }

  .sort_list {
    height: 96px;
    font-size: 28px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    color: #74798c;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 2px solid #eee;

    &.active {
      color: #076ee4;
    }

    image {
      width: 32px;
      height: 32px;
    }
  }
}
/* 写死标签 */
.threenav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  height: 96px;
  border-top: 1px solid #eee;
  background: #fff;
  span {
    display: flex;
    align-items: center;
    padding: 0 20px;
    background: #ffffff;
    border-radius: 8px;
    border: 2px solid #eeeeee;
    height: 58px;
    font-size: 24px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #74798c;
  }
  .end {
    border: 2px solid #e72410;
    color: #e72410;
    margin-left: 18px;
  }
}
.list {
  overflow: auto;
  background: #fff;
  border-top: 20px solid #f7f7f7;
  -ms-overflow-style: none; /* IE 10+ */
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
  .map {
    width: 100%;
    height: 626px;
    padding: 0 16px;
    .imgs {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 626px;
      width: 100%;
      font-size: 24px;
      color: #20263a;
      font-family: PingFang SC, PingFang SC-Regular;
      img {
        width: 290px;
        height: 230px;
        margin-bottom: 20px;
      }
    }
  }
}
// 企业推荐
.touzi {
  border-radius: 8px;
  overflow-y: scroll;
  scroll-behavior: smooth;
  background: #fff;
  margin: 20px 0;
  border-top: 20px solid #f7f7f7;
  &-t {
    background: #fff;
    position: relative;
    font-size: 32px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #20263a;
    display: flex;
    height: 100px;
    align-items: center;
    padding-left: 24px;
    border-bottom: 1px solid #eeeeee;
    margin: 0 24px 0;
  }
  &-c {
    background: #fff;
    margin: 0px 12px 0px;
    padding: 0 32px;
  }
  &-item {
    height: 96px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #eeeeee;
    font-size: 28px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #3d4255;
  }
}
</style>

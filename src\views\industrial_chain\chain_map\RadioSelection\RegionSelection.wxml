<!--components/RegionSelection/RegionSelection.wxml-->
<HalfScreenPop
  showCloseBtn="{{false}}"
  visible="{{visible}}"
  position="{{position}}"
  bindsubmit="submit"
  bindclose="close"
  startDistance="{{startDistance}}"
  disableAnimation="{{true}}"
  _maskClosable="{{false}}"
  zIndex="{{zIndex}}"
  footHeigh="110rpx"
>
  <view slot="customContent" class="area" style="margin-top:{{top}}px;">
    <!-- 容器保持固定高度 -->
    <view class="region-container">
      <!-- 占位内容 -->
      <view wx:if="{{!readyToShow}}" class="placeholder-content">
        <view class="placeholder-text">地区数据加载中...</view>
      </view>

      <!-- 主要内容 - 只有在准备好时才显示 -->
      <view wx:else class="region-wrap">
        <!-- 省 -->
        <view class="list province-list">
          <scroll-view scroll-y="true" style="height:100%">
            <view
              class="item province {{item.active && 'active'}} {{item.selected && 'selected'}}"
              bindtap="selectRegion"
              data-code="{{item.code}}"
              data-level="{{item.level}}"
              data-name="{{item.name}}"
              wx:for="{{regionObj.provinceList}}"
              wx:key="code"
            >
              <text
                class="{{item.selected && 'selected-text'}}"
                >{{item.name}}</text
              >
              <image
                class="checkmark {{item.selected && 'show'}}"
                wx:if="{{item.selected}}"
                src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_checked.png"
              ></image>
            </view>
          </scroll-view>
        </view>

        <!-- 市 -->
        <view class="list city-list">
          <scroll-view scroll-y="true" style="height:100%">
            <view
              class="item city {{item.active && 'active'}} {{item.selected && 'selected'}}"
              bindtap="selectRegion"
              data-code="{{item.code}}"
              data-level="{{item.level}}"
              data-name="{{item.name}}"
              data-isarea="{{item.isarea}}"
              wx:for="{{regionObj.curCityList}}"
              wx:key="code"
            >
              <text
                class="{{item.selected && 'selected-text'}}"
                >{{item.name}}</text
              >
              <image
                class="checkmark {{item.selected && 'show'}}"
                wx:if="{{item.selected}}"
                src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_checked.png"
              ></image>
            </view>
          </scroll-view>
        </view>

        <!-- 区 -->
        <view class="list area-list">
          <scroll-view scroll-y="true" style="height:100%">
            <view
              class="item area {{item.selected && 'selected'}}"
              bindtap="selectRegion"
              data-code="{{item.code}}"
              data-level="{{item.level}}"
              data-name="{{item.name}}"
              wx:for="{{regionObj.curAreaList}}"
              wx:key="code"
            >
              <text
                class="{{item.selected && 'selected-text'}}"
                >{{item.name}}</text
              >
              <image
                class="checkmark {{item.selected && 'show'}}"
                wx:if="{{item.selected}}"
                src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_checked.png"
              ></image>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>
  </view>
</HalfScreenPop>

<script setup name="AtlasList">
import { reactive, ref, onMounted, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useRect, useWindowSize } from '@vant/use'
// import Map from './ChainMap.vue'
// 暂时注释掉不存在的组件
// import RadioArea from '@/components/Hunt/component/radioIndustry/index.vue'
// import RadioIndustry from '@/components/Hunt/component/radioIndustry/indexIndustry.vue'
// import { constant } from '@/components/Hunt/component/MultiplecChoice/utils'
// import { setPx } from '@/utils/height'
// import { goH5 } from '@/utils/utils'
const { width, height } = useWindowSize()
const route = useRoute()
const MDUTCG_MAP = ['北京市', '重庆市', '天津市', '上海市']
const CHINA_CODE = '1000000'
const LEVEL_MAP = {
  province: '1',
  city: '2',
  district: '3',
}
const data = reactive({
  //请求的相关
  paramsNew: {
    chain_codes: [],
    page_index: 1,
    page_size: 10,
  },
  paramsData: {},
  sortVisible: false,
  popSort: [
    {
      name: '产业结构',
      show: false,
      isVal: false,
    },
    {
      name: '全国',
      show: false,
      isVal: false,
    },
  ],
  company_num: 0,
  // 弹窗相关
  region: {
    name: '全国',
    code: CHINA_CODE,
  },
  chain_codes: [],
  chain_code: '',
  agg_type: 2,
  tagArr: [],
  // 高度
  flistHeight: 0,
  listHeight: 0,
  huntHeight: 0,
  list: [], //热门推荐
  hotData: [], // 初始化热力数据
})
const listRef = ref(null)
const huntRef = ref(null)
const router = useRouter()
const mapShow = ref(true)
const { proxy } = getCurrentInstance()
// 获取相关高度
const getHeight = () => {
  let mapRef = useRect(listRef)
  data.flistHeight = mapRef.top
  data.listHeight = height.value - data.flistHeight - mapRef.height
  data.huntHeight = height.value - data.flistHeight
}
const _getHeatMapData = async (params = {}) => {
  const payload = {
    chain_codes: data.chain_codes,
    agg_type: 2,
    areas: [],
    ...params,
  }

  // Mock 热力图数据
  const mockHeatMapData = [
    { value: 1200, name: '广东省' },
    { value: 800, name: '江苏省' },
    { value: 600, name: '浙江省' },
    { value: 500, name: '山东省' },
    { value: 400, name: '河南省' },
    { value: 350, name: '四川省' },
    { value: 300, name: '湖北省' },
    { value: 280, name: '福建省' },
    { value: 250, name: '湖南省' },
    { value: 200, name: '安徽省' },
    { value: 180, name: '河北省' },
    { value: 150, name: '江西省' },
    { value: 120, name: '重庆市' },
    { value: 100, name: '陕西省' },
    { value: 80, name: '广西壮族自治区' },
  ]

  let count = mockHeatMapData.reduce((sum, item) => sum + item.value, 0)
  data.hotData = mockHeatMapData
  data.company_num = count
}
const queryData = region => {
  let agg_type = region.code === 'ALL' ? 2 : region.level === '1' ? 4 : 6
  if (region.code === 'ALL' /** 首次进入页面或点击地区选择器 全国 选项 */) {
    _getHeatMapData({
      areas: [],
      agg_type,
    })
  } else if (MDUTCG_MAP.includes(region.name) /** 选择的是直辖市， 可触发条件：点击地图块，地区选择器 */) {
    _getHeatMapData({
      areas: [region.code],
      agg_type,
    })
  } else if (region.level === '1' /** 选择的是省份， 可触发条件同上 */) {
    _getHeatMapData({
      areas: [region.code],
      agg_type,
    })
  } else if (region.level === '2' /** 选择的是市级，可触发条件同上 */) {
    _getHeatMapData({
      areas: [region.code],
      agg_type,
    })
  } /** 区县级，可触发条件同上 */ else {
    _getHeatMapData({
      areas: [region.code],
      agg_type,
    })
  }
  data.agg_type = agg_type
}

// 获取初始化参数-请求相关
const getParams = () => {
  // 先设置默认的热力数据，避免模板报错
  if (!data.hotData || data.hotData.length === 0) {
    data.hotData = []
  }

  // 适配当前项目的参数格式
  const { code, name } = route.query
  if (code && name) {
    // 构造数据结构
    const chainData = {
      code: code,
      name: name,
      high: '1', // mock数据
      leader_count: 1, // mock数据
    }

    data.chain_code = code
    data.chain_codes = [code]
    data.fchain_codes = [code]
    data.routeList = [chainData]
    data.tagArr = ['高价值产业环节', '有龙头企业'] // mock标签
    data.popSort[0].name = name.length > 7 ? name.slice(0, 7) + '...' : name
    data.popSort[0].isVal = true

    // 获取地区数组
    queryData({ code: 'ALL' })
  } else {
    // 默认数据
    data.routeList = [{ name: '智能网联新能源汽车产业', code: 'DEFAULT' }]
    data.tagArr = []
    queryData({ code: 'ALL' })
  }
}

// 筛选点击
const headTap = item => {
  let { popSort } = data
  let isshow = !popSort[item].show
  popSort.forEach(item => {
    item.show = false
  })
  popSort[item].show = isshow
  data.popSort = popSort
}
// 列表展示
const backRoute = () => {
  // 返回到产业链节点页面
  const { code, name } = route.query
  router.push({
    path: '/industrial_chain_node',
    query: {
      code: code,
      name: name,
    },
  })
}
// 回调
const chooseSort = item => {}
// 取消
const resetCondition = () => {}
// 确定 地图回调
const clickMap = item => {
  try {
    let datas = item?.data || {}
    let code = String(datas?.adcode || datas?.id)
    if (!code) return
    const level = LEVEL_MAP[datas.level]
    let curRegion = {
      code,
      level,
      parent: datas?.parent?.adcode + '',
      name: datas.name,
    }
    data.region = curRegion
    data.popSort[1].name = curRegion.name
    data.popSort[1].isVal = curRegion.name != '全国'
    queryData(curRegion)
  } catch (err) {}
}
const getRegion = item => {
  try {
    data.popSort[1].show = false
    let curRegion = item?.length ? item[0] : item
    data.region = curRegion
    data.popSort[1].name = curRegion.name
    data.popSort[1].isVal = curRegion.name != '全国'
    queryData(curRegion)
  } catch (err) {
    console.log('210', err)
    data.popSort[1].show = false
  }
}
const getqcCy = oldAry => {
  let datas = oldAry?.length ? oldAry[0] : oldAry
  let { chain_codes, popSort, region } = data
  popSort[0].show = false
  popSort[0].isVal = datas.name != '智能网联新能源汽车产业'
  popSort[0].name =
    datas.chain_code == 'ALL' ? '产业结构' : datas.name.length > 7 ? datas.name.slice(0, 7) + '...' : datas.name
  if (datas.chain_code == 'ALL') {
    chain_codes = data.fchain_codes
  } else {
    chain_codes = datas.chain_code ? [datas.chain_code] : []
  }
  data.popSort = popSort
  data.chain_codes = chain_codes || 'E1E1'
  _getHeatMapData({
    areas: datas.chain_code == 'ALL' ? [] : region.code == '1000000' ? [] : [region.code],
    agg_type: data.agg_type,
  })
}
const getqcCyAry = oldAry => {
  let { routeList } = data
  let arr = []
  routeList = routeList.slice(0, 1)
  let datas = oldAry?.length ? oldAry[0] : oldAry
  if (!oldAry?.length) return
  if (datas.chain_code != 'ALL') {
    arr = oldAry.map(item => {
      return {
        code: item.chain_code,
        high: item.high,
        leader_count: item.leader_count,
        name: item.name,
        strong_codes: item.strong_codes,
        weak_codes: item.weak_codes,
      }
    })
    routeList.push(...arr)
  }
  data.routeList = routeList
}
//
const getHotList = async () => {
  // Mock 企业推荐数据
  const mockEntList = [
    { ent_name: '比亚迪股份有限公司', ent_id: '1001' },
    { ent_name: '宁德时代新能源科技股份有限公司', ent_id: '1002' },
    { ent_name: '特斯拉(上海)有限公司', ent_id: '1003' },
    { ent_name: '蔚来汽车科技有限公司', ent_id: '1004' },
    { ent_name: '小鹏汽车科技有限公司', ent_id: '1005' },
    { ent_name: '理想汽车制造有限公司', ent_id: '1006' },
    { ent_name: '广汽埃安新能源汽车有限公司', ent_id: '1007' },
    { ent_name: '长城汽车股份有限公司', ent_id: '1008' },
    { ent_name: '吉利汽车控股有限公司', ent_id: '1009' },
    { ent_name: '上汽集团股份有限公司', ent_id: '1010' },
  ]

  data.list = mockEntList
  if (proxy && proxy.$close) {
    proxy.$close()
  }
}
const goDetail = item => {
  //进企业详情 - 暂时用alert代替
  if (!item.ent_id) return
  alert(`查看企业详情：${item.ent_name}`)
}
/**
 * 没发现地图
 */
const onFound = () => {
  mapShow.value = false
}
onMounted(() => {
  // 移除loading调用，因为proxy可能不存在
  // if (proxy && proxy.$loading) {
  //   proxy.$loading('加载中...')
  // }
  getParams()
  getHotList()
  getHeight()
})
</script>

<template>
  <div class="leading-page">
    <!-- 头部标签部分 -->
    <div class="biaoqianf">
      <div class="biaoqian">
        <template v-for="(item, index) in data.routeList" :key="index">
          <span :class="[index == data.routeList.length - 1 && 'one']">
            {{ item.name }}
          </span>
          <span class="two" v-if="index != data.routeList.length - 1"></span>
        </template>
      </div>
    </div>
    <!-- 筛选部分 -->
    <div class="twonav">
      <div class="company_num">
        共有 <span class="color_num">{{ data.company_num }}</span> 家企业
      </div>
      <div class="head_left">
        <div
          class="left_box"
          v-for="(item, index) in data.popSort"
          :key="index"
          :class="{ active: item.show || item.isVal }"
          @click="headTap(index)"
        >
          <span class="down_txt">{{ item.name }}</span>
          <div class="iconas flex_all_center" v-if="data.levelShow">
            <img :src="icon || '@assets/img/tupu/menu-up.png'" />
          </div>
          <img v-if="!item.show && !item.isVal" class="down_img" src="@/assets/img/tupu/menu-down.png" />
          <img v-if="!item.show && item.isVal" class="down_img" src="@/assets/img/tupu/menu-down-a.png" />
          <img v-if="item.show" class="down_img" src="@/assets/img/tupu/menu-up.png" />
        </div>
      </div>
    </div>
    <!-- 地图点击部分 -->
    <div class="threenav" ref="listRef" id="threenav">
      <template v-if="!data.tagArr.length">
        <view style="border: none"></view>
      </template>
      <template v-if="data.tagArr.length">
        <span v-for="(item, index) in data.tagArr" :key="index">{{ item }}</span>
      </template>
      <span class="end" @click="backRoute()">列表展示</span>
    </div>
    <!-- 列表 -->
    <div class="list" :style="{ height: data.listHeight + 'px' }">
      <!-- 地图 -->
      <div class="map">
        <!-- 暂时用占位图替代地图组件 -->
        <div class="map-placeholder">
          <div class="map-content">
            <h3>{{ data.region.name }}产业链地图</h3>
            <div class="heat-data" v-if="data.hotData && data.hotData.length > 0">
              <h4>热力数据分布：</h4>
              <div class="heat-list">
                <div v-for="item in data.hotData.slice(0, 8)" :key="item.name" class="heat-item">
                  <span class="region-name">{{ item.name }}</span>
                  <span class="region-value">{{ item.value }}家</span>
                </div>
              </div>
            </div>
            <div v-else class="loading-placeholder">
              <p>正在加载地图数据...</p>
            </div>
          </div>
        </div>
      </div>
      <!-- 企业推荐 -->
      <div class="touzi" v-if="data.list.length">
        <div class="touzi-t">推荐企业</div>
        <div class="touzi-c">
          <div v-for="(item, index) in data.list" :key="index">
            <div @click="goDetail(item)" class="touzi-item">{{ item.ent_name }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 暂时移除弹窗组件，简化展示 -->
</template>

<style lang="scss" scoped>
/* 返回路由样式 */
.biaoqianf {
  scroll-behavior: smooth;
  overflow-x: scroll;
  width: 100vw;
  white-space: nowrap;
  background: #f7f7f7;
  position: relative;
  z-index: 2100;
  -ms-overflow-style: none; /* IE 10+ */
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }

  .biaoqian {
    display: inline-flex;
    align-items: center;
    height: 82px;
    padding-left: 24px;
    padding-right: 24px;
    font-size: 24px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #9b9eac;
    width: auto;
    overflow-x: scroll;

    & .one {
      color: #3d4255;
    }

    .two {
      display: inline-block;
      margin: 0 8px;
      width: 16px;
      height: 2px;
      background: #9b9eac;
    }
  }
}
/*  */
.twonav {
  position: relative;
  height: 96px;
  box-sizing: border-box;
  display: flex;
  padding: 0 24px;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  position: relative;
  z-index: 2100;
  border-bottom: 1px solid #f7f7f7;
  box-sizing: border-box;

  .company_num {
    font-size: 28px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 600;
    text-align: center;
    color: #20263a;

    .color_num {
      color: #076ee4;
    }
  }

  .head_left {
    min-width: 246px;
    height: 96px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left_box {
      font-size: 28px;
      font-family: PingFang SC, PingFang SC-Regular;
      font-weight: 400;
      text-align: LEFT;
      color: #74798c;
      margin-right: 8px;

      &.active {
        color: #076ee4;
      }

      .down_txt {
        padding-right: 12px;
      }

      .down_img {
        width: 20px;
        height: 20px;
      }
    }
  }

  .sort_list {
    height: 96px;
    font-size: 28px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    color: #74798c;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 2px solid #eee;

    &.active {
      color: #076ee4;
    }

    image {
      width: 32px;
      height: 32px;
    }
  }
}
/* 写死标签 */
.threenav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  height: 96px;
  border-top: 1px solid #eee;
  background: #fff;
  span {
    display: flex;
    align-items: center;
    padding: 0 20px;
    background: #ffffff;
    border-radius: 8px;
    border: 2px solid #eeeeee;
    height: 58px;
    font-size: 24px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #74798c;
  }
  .end {
    border: 2px solid #e72410;
    color: #e72410;
    margin-left: 18px;
  }
}
.list {
  overflow: auto;
  background: #fff;
  border-top: 20px solid #f7f7f7;
  -ms-overflow-style: none; /* IE 10+ */
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
  .map {
    width: 100%;
    height: 626px;
    padding: 0 16px;

    .map-placeholder {
      height: 100%;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;

      .map-content {
        text-align: center;
        width: 100%;
        padding: 20px;

        h3 {
          font-size: 32px;
          color: #333;
          margin-bottom: 30px;
          font-weight: 600;
        }

        .heat-data {
          h4 {
            font-size: 24px;
            color: #666;
            margin-bottom: 20px;
          }

          .heat-list {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            max-width: 400px;
            margin: 0 auto;

            .heat-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              background: rgba(255, 255, 255, 0.8);
              padding: 8px 12px;
              border-radius: 6px;
              font-size: 20px;

              .region-name {
                color: #333;
              }

              .region-value {
                color: #076ee4;
                font-weight: 600;
              }
            }
          }
        }

        .loading-placeholder {
          padding: 40px 20px;
          text-align: center;

          p {
            font-size: 24px;
            color: #666;
          }
        }
      }
    }

    .imgs {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 626px;
      width: 100%;
      font-size: 24px;
      color: #20263a;
      font-family: PingFang SC, PingFang SC-Regular;
      img {
        width: 290px;
        height: 230px;
        margin-bottom: 20px;
      }
    }
  }
}
// 企业推荐
.touzi {
  border-radius: 8px;
  overflow-y: scroll;
  scroll-behavior: smooth;
  background: #fff;
  margin: 20px 0;
  border-top: 20px solid #f7f7f7;
  &-t {
    background: #fff;
    position: relative;
    font-size: 32px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #20263a;
    display: flex;
    height: 100px;
    align-items: center;
    padding-left: 24px;
    border-bottom: 1px solid #eeeeee;
    margin: 0 24px 0;
  }
  &-c {
    background: #fff;
    margin: 0px 12px 0px;
    padding: 0 32px;
  }
  &-item {
    height: 96px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #eeeeee;
    font-size: 28px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #3d4255;
  }
}
</style>

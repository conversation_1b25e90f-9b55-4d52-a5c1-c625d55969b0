<template>
  <div class="h-full flex flex-col overflow-x-hidden">
    <!-- 头部 -->
    <div class="flex-shrink-0">
      <Head
        v-model:search-value="searchKeyword"
        :search-placeholder="'请输入产业链关键字'"
        @search="handleSearch"
        @clear="handleClear"
      />
    </div>
    <!-- tab部分 -->
    <div :class="['tab', displayData.length === 0 ? 'bg-white' : 'bg-[#F7F7F7]']">
      <!-- 全部产业链 -->
      <div class="title">
        <div>全部产业链</div>
        <div>
          共<u>{{ chainNum }}</u
          >条产业链
        </div>
      </div>
      <!-- 加载中 -->
      <div v-if="loading" class="loading-container">
        <van-loading type="spinner" color="#07a6f0" size="24px" />
        <div class="loading-text ml-8">加载中</div>
      </div>
      <!-- 卡片 -->
      <div v-else-if="displayData.length > 0">
        <Card
          v-for="item in displayData"
          :key="item.chain_code"
          :title="item.name"
          :titCode="item.chain_code"
          :source="item.children"
          :searchKeyword="searchKeyword"
        />
      </div>
      <!-- 无数据提示 -->
      <div v-else class="noData">
        <img src="@/assets/image/null.png" alt="" />
        <div>暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script setup name="IndustrialChain">
import { ref, computed, onMounted } from 'vue'
import Head from './Head.vue'
import Card from './Card.vue'
import { getChainList } from '@/api/iChain/index'

// 响应式数据
const originalData = ref([]) // 原始完整数据
const searchKeyword = ref('') // 搜索关键字
const loading = ref(true) // 加载状态

// 计算属性 - 过滤后的数据
const displayData = computed(() => {
  if (!searchKeyword.value.trim()) {
    return originalData.value
  }

  // 搜索逻辑：在children中匹配关键字
  return originalData.value
    .map(parentChain => {
      const matchedChildren = parentChain.children.filter(child => child.name.includes(searchKeyword.value.trim()))

      if (matchedChildren.length > 0) {
        return {
          ...parentChain,
          children: matchedChildren,
        }
      }
      return null
    })
    .filter(Boolean) // 移除null项
})

// 计算属性 - 产业链节点总数
const chainNum = computed(() => {
  return displayData.value.reduce((total, chain) => {
    return total + (chain.children ? chain.children.length : 0)
  }, 0)
})

// 获取数据
const getData = async () => {
  try {
    loading.value = true
    const res = await getChainList()
    console.log('获取到的数据:', res)

    // 假设返回的数据结构是 { data: [...] } 或者直接是数组
    originalData.value = res.data || res || []
  } catch (error) {
    console.error('获取产业链数据失败:', error)
    originalData.value = []
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = keyword => {
  console.log('搜索关键字:', keyword)
  searchKeyword.value = keyword
}

// 清空搜索
const handleClear = () => {
  console.log('清空搜索')
  searchKeyword.value = ''
}

// 页面挂载时获取数据
onMounted(() => {
  getData()
})
</script>

<style lang="scss" scoped>
.tab {
  @apply flex-1 overflow-y-scroll;
  -ms-overflow-style: none; /* IE 10+ */
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
  .title {
    @apply w-full f-y-center h-108 bg-white rounded-8 border-b border-solid border-[#EBEBEB];
    div:first-child {
      font-weight: 600;
      font-size: 32px;
      color: #07a6f0;
      margin: 0 24px;
    }
    div:last-child {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 24px;
      color: #b2b2b2;
      u {
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 24px;
        color: #07a6f0;
        margin: 0 4px;
      }
    }
  }

  .loading-container {
    @apply flex items-center justify-center;
    height: calc(100vh - 370px);

    .loading-text {
      @apply text-28 text-secondary;
    }
  }
}
.noData {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: calc(100vh - 370px);
  // border: 1px solid red;
  img {
    width: 230px;
    height: 230px;
    transform: translateY(-180px);
  }
  div {
    color: #74798c;
    margin-top: 24px;
    transform: translateY(-180px);
  }
}
</style>

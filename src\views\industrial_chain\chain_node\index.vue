<template>
  <div class="relative h-full bg-[#F7F7F7] overflow-hidden">
    <div ref="headRef" class="head">
      <!-- 右边下拉框 -->
      <div class="dropdown-container">
        <div class="dropdown-trigger" @click="toggleDropdown">
          <span>{{ selectedChain.name || '请选择产业链' }}</span>
          <svg-icon
            name="arrow-fill"
            class="ml-8 transition-all duration-300 w-20 h-20"
            :class="[data.dropdownVisible ? 'rotate-180' : '']"
          />
        </div>
      </div>
      <div class="btn">地图展示</div>
    </div>

    <!-- tab -->
    <div ref="tabRef" class="tab-container">
      <div
        v-for="(item, index) in data.tab"
        :key="index"
        class="tab-item"
        :class="{ active: data.activeTab === item.value }"
        @click="handleTabClick(item.value)"
      >
        {{ item.name }}
      </div>
    </div>
    <!-- threeTab -->
    <div ref="threeTabRef" class="three-tab-container">
      <div
        v-for="(item, index) in data.threeTab"
        :key="index"
        class="three-tab-item"
        :class="{ active: data.activeThreeTab === item.code }"
        @click="handleThreeTabClick(item.code)"
      >
        {{ item.name }}
      </div>
    </div>

    <!-- 卡片 -->
    <div class="restH" :style="{ height: restHeight + 'px' }">
      <!-- 上游卡片 -->
      <Card v-if="data.activeThreeTab === 'up'" :source="data.upList" />

      <!-- 中游卡片 -->
      <Card v-else-if="data.activeThreeTab === 'middle'" :source="data.middleList" />

      <!-- 下游卡片 -->
      <Card v-else-if="data.activeThreeTab === 'down'" :source="data.downList" />
    </div>
    <!-- 下拉框组件 -->
    <ChainDropdown
      :visible="data.dropdownVisible"
      :selectedData="data.selectedChainData"
      :headHeight="headHeight"
      @close="closeDropdown"
      @submit="handleChainSelect"
    />
  </div>
</template>

<script setup>
import { reactive, computed, ref, onMounted, onUnmounted, nextTick } from 'vue'
import { getNodeList } from '@/api/iChain/index'
import { useRoute } from 'vue-router'
import Card from './Card.vue'
import ChainDropdown from './ChainDropdown.vue'

// 路由实例
const route = useRoute()

// 头部引用和高度
const headRef = ref(null)
const headHeight = ref(88) // 默认高度

// Tab 引用
const tabRef = ref(null)
const threeTabRef = ref(null)

// 剩余高度
const restHeight = ref(0)

const data = reactive({
  dropdownVisible: false,
  selectedChainData: [],
  activeTab: 'check_leading',
  activeThreeTab: 'up',
  tab: [
    {
      name: '有龙头企业',
      value: 'check_leading',
    },
    {
      name: '高价值环节',
      value: 'check_high',
    },
    {
      name: '优势环节',
      value: 'check_advantage',
    },
    {
      name: '弱势环节',
      value: 'check_weak',
    },
  ],
  threeTab: [
    {
      name: '上游',
      code: 'up',
    },
    {
      name: '中游',
      code: 'middle',
    },
    {
      name: '下游',
      code: 'down',
    },
  ],
  upList: [], // 上游数据
  middleList: [], // 中游数据
  downList: [], // 下游数据
})

const selectedChain = computed(() => {
  return data.selectedChainData[0] || { name: '请选择产业链' }
})

// 下拉框相关方法
const toggleDropdown = () => {
  data.dropdownVisible = !data.dropdownVisible
}

const closeDropdown = () => {
  data.dropdownVisible = false
}

const handleChainSelect = selectedList => {
  data.selectedChainData = selectedList
  data.dropdownVisible = false
}

// Tab相关方法
const handleTabClick = value => {
  data.activeTab = value
}

const handleThreeTabClick = code => {
  data.activeThreeTab = code
}

// 计算head高度
const calculateHeadHeight = () => {
  nextTick(() => {
    if (headRef.value) {
      headHeight.value = headRef.value.offsetHeight
    }
  })
}

// 计算剩余高度
const calculateRestHeight = () => {
  nextTick(() => {
    const windowHeight = window.innerHeight
    let usedHeight = 0

    // 头部高度
    if (headRef.value) {
      usedHeight += headRef.value.offsetHeight
    }

    // Tab 高度
    if (tabRef.value) {
      usedHeight += tabRef.value.offsetHeight
    }

    // ThreeTab 高度
    if (threeTabRef.value) {
      usedHeight += threeTabRef.value.offsetHeight
    }

    // 计算剩余高度
    restHeight.value = windowHeight - usedHeight
    console.log('窗口高度:', windowHeight, '已用高度:', usedHeight, '剩余高度:', restHeight.value)
  })
}

// 初始化路由参数
const getData = async () => {
  const { code, name } = route.query
  if (!code) return
  // 设置初始选中的产业链数据
  data.selectedChainData = [
    {
      chain_code: code,
      name: name,
    },
  ]
  const list = await getNodeList(code)
  // 将数据划分到上中下游
  if (!list.length) return
  const up = list.filter(item => item.belong_to === '1')
  const middle = list.filter(item => item.belong_to === '2')
  const down = list.filter(item => item.belong_to === '3')
  data.upList = up
  data.middleList = middle
  data.downList = down
  console.log(333, data.upList)

  // 数据加载完成后重新计算高度
  nextTick(() => {
    calculateRestHeight()
  })
}

// 窗口大小变化监听
const handleResize = () => {
  calculateRestHeight()
}

// 页面挂载后计算高度和初始化参数
onMounted(() => {
  calculateHeadHeight()
  calculateRestHeight()
  getData()

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)
})

// 页面卸载时移除监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.head {
  @apply relative f-all-center justify-between h-88 bg-white px-24 border-b border-solid border-[#eee];

  .dropdown-container {
    position: relative;
    height: 40px;

    .dropdown-trigger {
      @apply f-all-center cursor-pointer;
      min-width: 200px;
      font-weight: 400;
      font-size: 28px;
      color: #20263a;
    }
  }

  .btn {
    width: 144px;
    height: 56px;
    background: linear-gradient(90deg, #4ab8ec 0%, #07a6f0 100%);
    border-radius: 8px;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 24px;
    color: #ffffff;
    cursor: pointer;
    @apply f-all-center;
  }
}

// Tab样式 - 有背景有边框
.tab-container {
  @apply f-all-center bg-white p-24 justify-between;

  .tab-item {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 52px;
    min-width: 159px;
    padding: 0 8px;
    background: #f7f7f7;
    border: 1px solid #dedede;
    border-radius: 8px;
    font-weight: 400;
    font-size: 28px;
    color: #7f7f7f;
    cursor: pointer;
    transition: all 0.3s;

    &:last-child {
      margin-right: 0;
    }

    &.active {
      background: #f0faff;
      border-color: #07a6f0;
      color: #07a6f0;
    }

    &:hover:not(.active) {
      background: #f7f7f7;
      border-color: #dedede;
    }
  }
}

// ThreeTab样式 - 没有背景只有选中有下划线
.three-tab-container {
  @apply f-all-center  mt-20 mb-20 bg-white h-84;

  .three-tab-item {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    font-weight: 400;
    font-size: 28px;
    color: #74798c;
    height: 100%;
    cursor: pointer;
    position: relative;
    transition: all 0.3s;

    &:last-child {
      margin-right: 0;
    }

    &.active {
      color: #07a6f0;
      font-weight: 600;

      &::after {
        content: '';
        position: absolute;
        bottom: 0px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 6px;
        background: linear-gradient(90deg, #4ab8ec 0%, #07a6f0 100%);
      }
    }

    &:hover:not(.active) {
      color: #333;
    }
  }
}

// 剩余高度容器样式
.restH {
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;

  // 隐藏滚动条但保持滚动功能
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }

  // 兼容 Firefox
  scrollbar-width: none;
  -ms-overflow-style: none;
}
</style>

<template>
  <div class="relative h-full">
    <div ref="headRef" class="head">
      <!-- 右边下拉框 -->
      <div class="dropdown-container">
        <div class="dropdown-trigger" @click="toggleDropdown">
          <span>{{ selectedChain.name || '请选择产业链' }}</span>
          <svg-icon
            name="arrow-fill"
            class="ml-8 transition-all duration-300 w-20 h-20"
            :class="[data.dropdownVisible ? 'rotate-180' : '']"
          />
        </div>
      </div>
      <div class="btn">地图展示</div>
    </div>

    <!-- tab -->
    <div class="tab-container red">
      <div
        v-for="(item, index) in data.tab"
        :key="index"
        class="tab-item"
        :class="{ active: data.activeTab === item.value }"
        @click="handleTabClick(item.value)"
      >
        {{ item.name }}
      </div>
    </div>
    <!-- threeTab -->
    <div class="three-tab-container">
      <div
        v-for="(item, index) in data.threeTab"
        :key="index"
        class="three-tab-item"
        :class="{ active: data.activeThreeTab === item.code }"
        @click="handleThreeTabClick(item.code)"
      >
        {{ item.name }}
      </div>
    </div>

    <!-- 卡片 -->
    <div>
      <!-- 上游卡片 -->
      <Card v-if="data.activeThreeTab === 'up'" key="up"> </Card>

      <!-- 中游卡片 -->
      <Card v-else-if="data.activeThreeTab === 'middle'" key="middle"> </Card>

      <!-- 下游卡片 -->
      <Card v-else-if="data.activeThreeTab === 'down'" key="down"> </Card>
    </div>
    <!-- 下拉框组件 -->
    <ChainDropdown
      :visible="data.dropdownVisible"
      :selectedData="data.selectedChainData"
      :headHeight="headHeight"
      @close="closeDropdown"
      @submit="handleChainSelect"
    />
  </div>
</template>

<script setup>
import { reactive, computed, ref, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import Card from './Card.vue'
import ChainDropdown from './ChainDropdown.vue'

// 路由实例
const route = useRoute()

// 头部引用和高度
const headRef = ref(null)
const headHeight = ref(88) // 默认高度

const data = reactive({
  dropdownVisible: false,
  selectedChainData: [],
  activeTab: 'check_leading',
  activeThreeTab: 'up',
  tab: [
    {
      name: '有龙头企业',
      value: 'check_leading',
    },
    {
      name: '高价值环节',
      value: 'check_high',
    },
    {
      name: '优势环节',
      value: 'check_advantage',
    },
    {
      name: '弱势环节',
      value: 'check_weak',
    },
  ],
  threeTab: [
    {
      name: '上游',
      code: 'up',
    },
    {
      name: '中游',
      code: 'middle',
    },
    {
      name: '下游',
      code: 'down',
    },
  ],
})

const selectedChain = computed(() => {
  return data.selectedChainData[0] || { name: '请选择产业链' }
})

// 下拉框相关方法
const toggleDropdown = () => {
  data.dropdownVisible = !data.dropdownVisible
}

const closeDropdown = () => {
  data.dropdownVisible = false
}

const handleChainSelect = selectedList => {
  data.selectedChainData = selectedList
  data.dropdownVisible = false
}

// Tab相关方法
const handleTabClick = value => {
  data.activeTab = value
}

const handleThreeTabClick = code => {
  data.activeThreeTab = code
}

// 计算head高度
const calculateHeadHeight = () => {
  nextTick(() => {
    if (headRef.value) {
      headHeight.value = headRef.value.offsetHeight
    }
  })
}

// 初始化路由参数
const initRouteParams = () => {
  const { code, name } = route.query
  if (code && name) {
    // 设置初始选中的产业链数据
    data.selectedChainData = [
      {
        code: code,
        chain_code: code,
        name: name,
      },
    ]
  }
}

// 页面挂载后计算高度和初始化参数
onMounted(() => {
  calculateHeadHeight()
  initRouteParams()
})
</script>

<style lang="scss" scoped>
.head {
  @apply relative f-all-center justify-between h-88 bg-white px-24 border-b border-solid border-[#eee];

  .dropdown-container {
    position: relative;
    height: 40px;

    .dropdown-trigger {
      @apply f-all-center cursor-pointer;
      min-width: 200px;
      font-weight: 400;
      font-size: 28px;
      color: #20263a;
    }
  }

  .btn {
    width: 144px;
    height: 56px;
    background: linear-gradient(90deg, #4ab8ec 0%, #07a6f0 100%);
    border-radius: 8px;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 24px;
    color: #ffffff;
    cursor: pointer;
    @apply f-all-center;
  }
}

// Tab样式 - 有背景有边框
.tab-container {
  @apply f-all-center bg-white px-24 py-16;

  .tab-item {
    padding: 12px 24px;
    margin-right: 16px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    transition: all 0.3s;

    &:last-child {
      margin-right: 0;
    }

    &.active {
      background: linear-gradient(90deg, #4ab8ec 0%, #07a6f0 100%);
      border-color: #07a6f0;
      color: white;
    }

    &:hover:not(.active) {
      background: #e9ecef;
      border-color: #dee2e6;
    }
  }
}

// ThreeTab样式 - 没有背景只有选中有下划线
.three-tab-container {
  @apply f-all-center bg-white px-24 py-16 border-b border-solid border-[#eee];

  .three-tab-item {
    padding: 12px 24px;
    margin-right: 32px;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    position: relative;
    transition: all 0.3s;

    &:last-child {
      margin-right: 0;
    }

    &.active {
      color: #07a6f0;
      font-weight: 600;

      &::after {
        content: '';
        position: absolute;
        bottom: -16px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 3px;
        background: linear-gradient(90deg, #4ab8ec 0%, #07a6f0 100%);
        border-radius: 2px;
      }
    }

    &:hover:not(.active) {
      color: #333;
    }
  }
}
</style>

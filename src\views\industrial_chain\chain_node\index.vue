<template>
  <div class="relative h-full bg-[#F7F7F7] overflow-hidden flex flex-col">
    <div ref="headRef" class="head">
      <!-- 右边下拉框 -->
      <div class="dropdown-container">
        <div class="dropdown-trigger" @click="toggleDropdown">
          <span>{{ selectedChain.name || '请选择产业链' }}</span>
          <svg-icon
            name="arrow-fill"
            class="ml-8 transition-all duration-300 w-20 h-20"
            :class="[data.dropdownVisible ? 'rotate-180' : '']"
          />
        </div>
      </div>
      <div class="btn">地图展示</div>
    </div>

    <!-- tab -->
    <div ref="tabRef" class="tab-container">
      <div
        v-for="(item, index) in data.tab"
        :key="index"
        class="tab-item"
        :class="{ active: data.activeTab.includes(item.value) }"
        @click="handleTabClick(item.value)"
      >
        {{ item.name }}
      </div>
    </div>
    <!-- threeTab -->
    <div ref="threeTabRef" class="three-tab-container">
      <div
        v-for="(item, index) in data.threeTab"
        :key="index"
        class="three-tab-item"
        :class="{ active: data.activeThreeTab === item.code }"
        @click="handleThreeTabClick(item.code)"
      >
        {{ item.name }}
      </div>
    </div>

    <!-- 卡片 -->
    <div ref="restRef" class="restH flex-1" :style="{ height: restHeight + 'px' }">
      <!-- 上游卡片 -->
      <Card v-if="data.activeThreeTab === 'up'" :source="data.upList" />

      <!-- 中游卡片 -->
      <Card v-else-if="data.activeThreeTab === 'middle'" :source="data.middleList" />

      <!-- 下游卡片 -->
      <Card v-else-if="data.activeThreeTab === 'down'" :source="data.downList" />
    </div>
    <!-- 下拉框组件 -->
    <ChainDropdown
      :visible="data.dropdownVisible"
      :selectedData="data.selectedChainData"
      :headHeight="headHeight"
      @close="closeDropdown"
      @submit="handleChainSelect"
    />
  </div>
</template>

<script setup>
import { reactive, computed, ref, onMounted, onUnmounted, nextTick } from 'vue'
import { getNodeList } from '@/api/iChain/index'
import { useRoute } from 'vue-router'
import Card from './Card.vue'
import ChainDropdown from './ChainDropdown.vue'

// 路由实例
const route = useRoute()

// 头部引用和高度
const headRef = ref(null)
const headHeight = ref(88) // 默认高度

// Tab 引用
const tabRef = ref(null)
const threeTabRef = ref(null)
const restRef = ref(null)

// 剩余高度
const restHeight = ref(0)

const data = reactive({
  dropdownVisible: false,
  selectedChainData: [],
  activeTab: [], // 改为数组，支持多选
  activeThreeTab: 'up',
  tab: [
    {
      name: '有龙头企业',
      value: 'check_leading',
    },
    {
      name: '高价值环节',
      value: 'check_high',
    },
    {
      name: '优势环节',
      value: 'check_advantage',
    },
    {
      name: '弱势环节',
      value: 'check_weak',
    },
  ],
  threeTab: [
    {
      name: '上游',
      code: 'up',
    },
    {
      name: '中游',
      code: 'middle',
    },
    {
      name: '下游',
      code: 'down',
    },
  ],
  upList: [], // 上游数据
  middleList: [], // 中游数据
  downList: [], // 下游数据
})

const selectedChain = computed(() => {
  return data.selectedChainData[0] || { name: '请选择产业链' }
})

// 下拉框相关方法
const toggleDropdown = () => {
  data.dropdownVisible = !data.dropdownVisible
}

const closeDropdown = () => {
  data.dropdownVisible = false
}

const handleChainSelect = selectedList => {
  data.selectedChainData = selectedList
  data.dropdownVisible = false
}

// Tab相关方法
const handleTabClick = value => {
  if (value === 'check_weak') {
    // 选择弱势环节：清空其他选择，只保留弱势
    data.activeTab = ['check_weak']
  } else {
    // 选择其他条件：移除弱势环节，切换当前条件
    data.activeTab = data.activeTab.filter(item => item !== 'check_weak')
    // 切换当前条件的选中状态
    if (data.activeTab.includes(value)) {
      data.activeTab = data.activeTab.filter(item => item !== value)
    } else {
      data.activeTab.push(value)
    }
  }
  // 重新处理数据
  processListData()
}

const handleThreeTabClick = code => {
  data.activeThreeTab = code

  // 切换时滚动到顶部
  nextTick(() => {
    if (restRef.value) {
      restRef.value.scrollTop = 0
    }
  })
}

// 数据处理函数：根据筛选条件给节点添加 isHeight 字段
const processListData = () => {
  const checkConditions = item => {
    // 如果没有选中任何条件，返回 false
    if (data.activeTab.length === 0) {
      return false
    }

    // 检查是否满足所有选中的条件（且关系）
    return data.activeTab.every(condition => {
      switch (condition) {
        case 'check_leading':
          return item.has_ent == 1
        case 'check_high':
          return item.high === '1'
        case 'check_advantage':
          return item.strong_codes && item.strong_codes.includes('440306')
        case 'check_weak':
          return item.weak_codes && item.weak_codes.includes('440306')
        default:
          return false
      }
    })
  }

  // 处理三个列表，给每个节点添加 isHeight 字段
  data.upList = data.upList.map(item => ({
    ...item,
    isHeight: checkConditions(item),
  }))

  data.middleList = data.middleList.map(item => ({
    ...item,
    isHeight: checkConditions(item),
  }))

  data.downList = data.downList.map(item => ({
    ...item,
    isHeight: checkConditions(item),
  }))
}

// 计算head高度
const calculateHeadHeight = () => {
  nextTick(() => {
    if (headRef.value) {
      headHeight.value = headRef.value.offsetHeight
    }
  })
}

// 计算剩余高度
const calculateRestHeight = () => {
  nextTick(() => {
    // 使用更精确的方法计算
    setTimeout(() => {
      const windowHeight = window.innerHeight
      let usedHeight = 0

      // 头部高度
      if (headRef.value) {
        const headRect = headRef.value.getBoundingClientRect()
        usedHeight += headRect.height
      }

      // Tab 高度
      if (tabRef.value) {
        const tabRect = tabRef.value.getBoundingClientRect()
        usedHeight += tabRect.height
      }

      // ThreeTab 高度
      if (threeTabRef.value) {
        const threeTabRect = threeTabRef.value.getBoundingClientRect()
        usedHeight += threeTabRect.height
      }

      // 预留一些安全边距，避免计算误差
      const safeMargin = 20

      // 计算剩余高度
      restHeight.value = Math.max(100, windowHeight - usedHeight - safeMargin)
    }, 100) // 延迟一点确保DOM渲染完成
  })
}

// 初始化路由参数
const getData = async () => {
  const { code, name } = route.query
  if (!code) return
  // 设置初始选中的产业链数据
  data.selectedChainData = [
    {
      chain_code: code,
      name: name,
    },
  ]
  const list = await getNodeList(code)
  // 将数据划分到上中下游
  if (!list.length) return
  const up = list.filter(item => item.belong_to === '1')
  const middle = list.filter(item => item.belong_to === '2')
  const down = list.filter(item => item.belong_to === '3')
  data.upList = up
  data.middleList = middle
  data.downList = down
  // 数据加载完成后处理筛选逻辑和重新计算高度
  nextTick(() => {
    processListData() // 处理筛选条件
    calculateRestHeight()
  })
}

// 窗口大小变化监听
const handleResize = () => {
  calculateRestHeight()
}

// 页面挂载后计算高度和初始化参数
onMounted(() => {
  calculateHeadHeight()
  calculateRestHeight()
  getData()

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)
})

// 页面卸载时移除监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.head {
  @apply relative f-all-center justify-between h-88 bg-white px-24 border-b border-solid border-[#eee];

  .dropdown-container {
    position: relative;
    height: 40px;

    .dropdown-trigger {
      @apply f-all-center cursor-pointer;
      min-width: 200px;
      font-weight: 400;
      font-size: 28px;
      color: #20263a;
    }
  }

  .btn {
    width: 144px;
    height: 56px;
    background: linear-gradient(90deg, #4ab8ec 0%, #07a6f0 100%);
    border-radius: 8px;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 24px;
    color: #ffffff;
    cursor: pointer;
    @apply f-all-center;
  }
}

// Tab样式 - 有背景有边框
.tab-container {
  @apply f-all-center bg-white p-24 justify-between;

  .tab-item {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 52px;
    min-width: 159px;
    padding: 0 8px;
    background: #f7f7f7;
    border: 1px solid #dedede;
    border-radius: 8px;
    font-weight: 400;
    font-size: 28px;
    color: #7f7f7f;
    cursor: pointer;
    transition: all 0.3s;

    &:last-child {
      margin-right: 0;
    }

    &.active {
      background: #f0faff;
      border-color: #07a6f0;
      color: #07a6f0;
    }

    &:hover:not(.active) {
      background: #f7f7f7;
      border-color: #dedede;
    }
  }
}

// ThreeTab样式 - 没有背景只有选中有下划线
.three-tab-container {
  @apply f-all-center  mt-20 mb-20 bg-white h-84;

  .three-tab-item {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    font-weight: 400;
    font-size: 28px;
    color: #74798c;
    height: 100%;
    cursor: pointer;
    position: relative;
    transition: all 0.3s;

    &:last-child {
      margin-right: 0;
    }

    &.active {
      color: #07a6f0;
      font-weight: 600;

      &::after {
        content: '';
        position: absolute;
        bottom: 0px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 6px;
        background: linear-gradient(90deg, #4ab8ec 0%, #07a6f0 100%);
      }
    }

    &:hover:not(.active) {
      color: #333;
    }
  }
}

// 剩余高度容器样式
.restH {
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;

  // 隐藏滚动条但保持滚动功能
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }

  // 兼容 Firefox
  scrollbar-width: none;
  -ms-overflow-style: none;
}
</style>
